## Product Requirements Document: Emoji Battery

**Version:** 1.0
**Date:** June 18, 2025
**Author:** DucPH
**Status:** Proposed

### 1. Overview

The Emoji Battery is a user-facing personalization feature that allows users to replace their standard Android status bar battery icon with a dynamic, customizable, and visually engaging emoji/character-based indicator. This feature aims to increase user engagement and provide a unique personalization option that sets our app apart, creating a more delightful user experience.

### 2. Goals and Objectives

*   **Primary Goal:** Increase daily active users (DAU) and session duration by providing a compelling and "sticky" personalization feature.
*   **Secondary Goal:** Create a new revenue stream through premium/exclusive battery styles.
*   **User Goal:** Allow users to express their personality and style by deeply customizing a core UI element of their phone.

### 3. Target Audience

Android users who enjoy UI customization, aesthetics, and personalization. This includes, but is not limited to:
*   Younger demographics (teens, young adults) who are fans of pop culture characters (e.g., Sanrio).
*   "Power users" who want to modify every aspect of their phone's appearance.
*   Users looking for a more fun and less utilitarian mobile experience.

### 4. User Stories

*   **As a new user, I want to** browse a gallery of different emoji battery styles so that I can find one that matches my taste.
*   **As a creative user, I want to** customize the selected style by changing the character, battery appearance, and text so that I can create a unique look.
*   **As any user, I want to** see a live preview of my changes before applying them so I know exactly how it will look.
*   **As a user with the feature enabled, I want to** still be able to access my notifications and control center by swiping down from the top so that I don't lose core phone functionality.
*   **As a user, I want to** easily enable or disable the entire feature with a single toggle so I can switch back to the default system UI whenever I want.
*   **As a free user, I want to** have access to a good selection of free styles, with the option to unlock premium styles (e.g., by watching an ad or paying).
*   **As a user, I want to** discover new and seasonal emoji battery styles without having to update the app, so I always have fresh options to choose from.


### 5. Functional Requirements

#### 5.1. Main Screen (Battery Gallery)

*   **Layout:** A grid-based view showcasing available Emoji Battery styles.
*   **Categorization:** Styles will be organized into filterable tabs (e.g., 🔥 HOT, Character, Heart, Cute, etc.). The "HOT" category should feature popular or trending styles.
*   **Global Toggle:** A prominent toggle switch at the top to "Enable or disable the emoji battery" feature globally.
*   **Style Indicators:**
    *   Each item in the grid is a self-contained preview of the style.
    *   Premium styles must be clearly marked with a "premium" icon (e.g., the diamond 💎 shown in the screenshots).
*   **Navigation:** Tapping on any style (free or premium) navigates the user to the Customization Screen.

#### 5.2. Customization Screen

*   **Live Preview:** A large preview area at the top of the screen that updates in real-time as the user changes customization options. It should show the emoji/character, the battery graphic, and the percentage text at a sample level (e.g., 50%).
*   **Style Selection:**
    *   **Battery:** A horizontal scrollable list of different battery container styles.
    *   **Emoji:** A horizontal scrollable list of different emojis/characters that can be paired with the selected battery.
    *   Selections should be visually highlighted. Premium options must be marked. If a premium option is selected, a purchase/unlock flow should be initiated upon tapping "Apply".
*   **Customization Toggles:**
    *   `Show Emoji`: Toggles the visibility of the character/emoji.
    *   `Show battery percentage`: Toggles the visibility of the numerical percentage text.
*   **Customization Sliders:**
    *   `Percentage`: Adjusts the font size of the percentage text (e.g., range from 5dp to 40dp).
    *   `Emoji Battery`: Adjusts the size of the emoji/character relative to the battery graphic.
*   **Color Picker:**
    *   `Color percentage`: A button that opens a color palette to allow the user to change the color of the percentage text.
*   **Apply Button:** A prominent "Apply" button at the bottom. Tapping this button saves the configuration and activates the Overlay UI.

#### 5.3. The Overlay UI (The active Emoji Battery)

*   **Display:** When enabled, the feature will display a custom UI overlay at the top of the screen, covering the native Android status bar.
*   **Content:** The overlay will display the user's configured Emoji Battery style. The battery level and character animation must dynamically reflect the phone's actual battery percentage.
*   **Interaction (CRITICAL):** The overlay must not block access to the system's Notification Center and Control Center (Quick Settings).
    *   **Requirement:** A downward swipe gesture anywhere on the overlay UI must trigger the standard system behavior of pulling down the notification shade.
*   ✨ **Implementation Strategy:**
    *   For devices running **Android 8.0 (API 26) or higher**, the overlay will be implemented using `WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY`. This binds the overlay's permission to the Accessibility Service itself.
    *   For devices running **versions below Android 8.0**, the overlay will require the `SYSTEM_ALERT_WINDOW` permission in addition to the Accessibility Service.

#### 5.4. Permissions

The app must request the following sensitive permissions with clear, user-friendly explanations, using a conditional approach based on the Android OS version.

1.  **Accessibility Service (Required for all versions):**
    *   **Justification:** "To display the emoji battery at the top of your screen and to detect swipes to open your notification panel." This is the core permission for the feature to function.

2.  **Display Over Other Apps (`SYSTEM_ALERT_WINDOW`) (Conditionally Required):**
    *   **Requirement:** This permission will **only** be requested on devices running Android versions **below 8.0 (API 26)**.
    *   **Justification:** "On your version of Android, this permission is needed to show the emoji battery on top of other apps."

#### ✨ 5.5. Content Management and Delivery
This section outlines the strategy for loading and managing the lists of available emojis, battery styles, and their associated properties (e.g., categories, premium status).

*   **Primary Source - Firebase Remote Config:**
    *   The complete list of emoji battery styles, including their image URLs, names, categories, and premium status, will be fetched from Firebase Remote Config on app launch.
    *   **Benefits:** This allows the product team to add new content, run A/B tests on styles, feature seasonal/promotional items, and change categories without requiring users to update the application through the Play Store.

*   **Fallback Mechanism - Local JSON:**
    *   The application must bundle a local `emoji-battery-styles.json` file with a default set of emoji battery styles.
    *   This local file will be used as an immediate fallback if:
        *   The Firebase Remote Config fetch fails due to network errors.
        *   The fetch times out (e.g., takes longer than 3 seconds).
        *   The user is offline.
    *   This ensures the feature is always functional and provides a seamless user experience even without an internet connection. The bundled JSON should be updated with each new app release.

*   **Data Schema Consistency:**
    *   The data structure (schema) of the content defined in Firebase Remote Config must be identical to the structure of the local `emoji-battery-styles.json` file. This is critical for the fallback mechanism to work without errors.

*   **Caching Strategy:**
    *   The system should leverage the built-in caching capabilities of the Firebase Remote Config SDK. On subsequent app launches, the cached configuration will be loaded instantly for a fast user experience, while a fresh fetch is initiated in the background.


### 6. Monetization

*   **Banner Ads:** A banner ad will be displayed at the bottom of the main gallery and customization screens.
*   **Premium Content (Freemium Model):**
    *   A subset of battery styles and emojis/characters will be marked as "Premium".
    *   **Unlock Mechanism:** Users can unlock premium content via:
        *   In-app purchase (e.g., "Unlock all" pack).
        *   Watching a rewarded video ad to unlock a single item for a limited time or permanently.
*   **"ADS ON/OFF" Toggle:** This suggests an ad-free subscription or one-time purchase to remove all ads from the app.

### 7. Success Metrics

*   **Adoption Rate:** Percentage of users who enable the Emoji Battery feature at least once.
*   **Engagement:** Average number of style changes and customizations per user per week.
*   **Retention:** 7-day and 30-day retention rate for users who have the feature enabled.
*   **Monetization:**
    *   Conversion rate from free to premium users.
    *   Revenue generated from premium content and ad removal.
    *   Number of rewarded ads watched per DAU.

### 8. Out of Scope for V1.0

*   Replicating all other status bar icons (Wi-Fi, network signal, time, notification icons). V1.0 will focus solely on replacing the battery indicator and providing the swipe-down gesture. The native status bar will be completely hidden by the overlay.
*   User-generated content (uploading custom emojis/styles).
*   Complex charging animations (V1.0 will show a static charging state or simple animation).
*   Integration with system themes (light/dark mode).

## Implementation Plan

This plan is divided into lean, independently compilable, and testable phases. It follows Clean Architecture (Presentation, Domain, Data layers), MVI, and the specified coding conventions. Files will go to app/src/main/java/com/tqhit/battery/one/features/emoji/

### Phase 0: Project Setup & Module Integration ✅ COMPLETED

**Goal:** Prepare the project structure for the new feature.

*   **Task 0.1: Create Feature Module Structure** ✅
    *   **Action:** Create the directory `features/emoji/` with subdirectories: `data`, `di`, `domain`, `presentation`. Inside `presentation`, create `gallery` and `customize` subdirectories.
*   **Task 0.2: Create DI Module** ✅
    *   **File:** `features/emoji/di/EmojiBatteryDIModule.kt`
    *   **Action:** Create a Hilt module to provide dependencies for the feature. Initially, it will be empty but will be populated in subsequent phases.
*   **Task 0.3: Unit Tests Setup** ✅
    *   **Action:** Create test directory structure and basic unit tests for DI module
*   **Task 0.4: Integration Verification** ✅
    *   **Action:** Verify module integrates with existing Hilt setup and compiles successfully

---

### Phase 1: Core Data Models & Data Layer Foundation

**Goal:** Establish the data foundation for fetching emoji styles.

*   **Task 1.1: Define Domain Models** 
    *   **Files:** `features/emoji/domain/model/BatteryStyle.kt`, `features/emoji/domain/model/BatteryStyleCategory.kt`
    *   **Action:** Implement the data classes as planned.

*   **Task 1.2: Define Domain Repository Interface** 
    *   **File:** `features/emoji/domain/repository/BatteryStyleRepository.kt`
    *   **Action:** Implement the repository interface as planned.

*   **Task 1.3: Implement Data Layer Repository** 
    *   **File:** `features/emoji/data/repository/BatteryStyleRepositoryImpl.kt`
    *   **Action:** Implement the class. It will inject `FirebaseRemoteConfigHelper` (already provided in the app) and `@ApplicationContext context: Context`.

*   **Task 1.4: Create Local Fallback Asset** 
    *   **File:** `app/src/main/assets/emoji_battery_styles.json`
    *   **Action:** Create the fallback JSON file.

*   **Task 1.5: Add Repository to DI Module**
    *   **File:** `features/emoji/di/EmojiBatteryDIModule.kt`
    *   **Action:** Add a `@Binds` function to provide `BatteryStyleRepositoryImpl` for the `BatteryStyleRepository` interface.

---

### Phase 2: Displaying Battery Styles (Gallery Screen) ✅

**Status:** Completed (2025-06-19)
**Goal:** Build the UI to display the list of emoji styles and integrate it into the app's main navigation.

#### Implementation Summary
Phase 2 has been successfully completed with all core functionality implemented:
- ✅ Domain use case with comprehensive error handling
- ✅ MVI architecture with state and event management
- ✅ Gallery ViewModel with CoreBatteryStatsService integration
- ✅ Fragment and layouts with Material 3 design
- ✅ RecyclerView adapter with Glide image loading and fallback handling
- ✅ Navigation integration with MainActivity and DynamicNavigationManager
- ✅ Unit tests for all components
- ✅ ADB testing documentation
- ✅ Build compilation successful

#### Error Handling for Missing Assets
Implemented comprehensive fallback system:
- Placeholder URL: `https://emoji.aranja.com/static/emoji-data/img-apple-160/1f600.png`
- Local emoji file placement for fallbacks:
  - Assets: `app/src/main/assets/emoji/`
  - Internal storage: `Android/data/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/files/emoji/`
  - Cache: `Android/data/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/cache/emoji/`

#### Known Limitations
- SwipeRefreshLayout temporarily removed (dependency issue)
- Premium unlock flows have placeholder implementations
- Navigation to customization screen pending Phase 3

*   **Task 2.1: Create Domain Use Case** ✅
    *   **File:** `features/emoji/domain/use_case/GetBatteryStylesUseCase.kt`
    *   **Status:** Completed with comprehensive error handling and reactive Flow integration

*   **Task 2.2: Implement Gallery MVI Components** ✅
    *   **Files:** `features/emoji/presentation/gallery/BatteryGalleryState.kt`, `features/emoji/presentation/gallery/BatteryGalleryEvent.kt`
    *   **Status:** Completed with full state management and event handling

*   **Task 2.3: Implement Gallery ViewModel** ✅
    *   **File:** `features/emoji/presentation/gallery/BatteryGalleryViewModel.kt`
    *   **Status:** Completed with CoreBatteryStatsService integration and debounced search

*   **Task 2.4: Create Gallery Fragment and Layout** ✅
    *   **Files:** `features/emoji/presentation/gallery/EmojiBatteryFragment.kt`, `res/layout/fragment_emoji_battery.xml`
    *   **Status:** Completed with Material 3 design and comprehensive UI components

*   **Task 2.5: Create RecyclerView Adapter** ✅
    *   **File:** `features/emoji/presentation/gallery/adapter/BatteryStyleAdapter.kt`
    *   **Status:** Completed with Glide image loading, fallback handling, and error management

*   **Task 2.6: Integrate into Main Navigation** ✅
    *   **Files:** `activity/main/MainActivity.kt`, `features/navigation/DynamicNavigationManager.kt`, `features/navigation/NavigationState.kt`
    *   **Status:** Completed with full navigation integration and fragment lifecycle management

---

### Phase 3: Style Customization Screen

**Goal:** Build the customization screen and manage user preferences.

*   **Task 3.1: Define and Implement Customization Persistence**
    *   **Files:** `features/emoji/domain/model/CustomizationConfig.kt`, `features/emoji/domain/repository/CustomizationRepository.kt`, `features/emoji/data/repository/CustomizationRepositoryImpl.kt`
    *   **Action:** Define the `CustomizationConfig` data class. Create the repository interface and implementation. Use **Jetpack DataStore** for persistence as it's the modern standard and can coexist with the app's `PreferencesHelper`.

*   **Task 3.2: Create Customization Use Cases** 
    *   **Files:** `features/emoji/domain/use_case/SaveCustomizationUseCase.kt`, `features/emoji/domain/use_case/LoadCustomizationUseCase.kt`

*   **Task 3.3: Implement Customize Screen MVI & UI** 
    *   **Files:** `features/emoji/presentation/customize/*`

*   **Task 3.4: Setup Navigation**
    *   **Action:** Implement navigation from `EmojiBatteryFragment` to `CustomizeFragment` using a standard fragment transaction. Do not use `nav_graph.xml` as the main navigation is handled programmatically.

---

### Phase 4: The Overlay Service

**Goal:** Implement the overlay using the correct system APIs and integrate with the existing battery data provider.

*   **Task 4.1: Implement the Custom View** 
    *   **File:** `features/emoji/presentation/overlay/EmojiBatteryView.kt`
    *   **Action:** Create the custom `View` class with `onDraw` logic.

*   **Task 4.2: Implement the Accessibility Service**
    *   **File:** `features/emoji/presentation/overlay/EmojiBatteryAccessibilityService.kt`
    *   **Action:** Implement the service as planned, using `TYPE_ACCESSIBILITY_OVERLAY` on API 26+ and falling back to `TYPE_PHONE` (with `SYSTEM_ALERT_WINDOW` permission) on older versions. This approach is correct for the feature's goal.

*   **Task 4.3: Integrate with Core Battery Provider** (Critical Update)
    *   **File:** `features/emoji/presentation/overlay/EmojiBatteryAccessibilityService.kt`
    *   **Action:**
        1.  **Inject `CoreBatteryStatsProvider`** into the service.
        2.  In the service's `onCreate` or `onServiceConnected`, launch a coroutine to **collect `coreBatteryStatsProvider.coreBatteryStatusFlow`**.
        3.  When a new `CoreBatteryStatus` is emitted, update the `EmojiBatteryView` with the new percentage.
        4.  **Do NOT create a new `BroadcastReceiver` for `ACTION_BATTERY_CHANGED`**. This is inefficient and goes against the existing architecture.

*   **Task 4.4: Implement Conditional Permission and Service Management** 
    *   **Action:** Implement the logic in the `EmojiBatteryFragment` and `CustomizeFragment` to handle permissions (Accessibility and, if needed, Overlay) and to start/stop the `EmojiBatteryAccessibilityService`.

---

### Phase 5: Monetization & Polish

**Goal:** Integrate ads and premium features consistent with the existing app.

*   **Task 5.1: Integrate Banner Ads**
    *   **Update:** Use the existing `ApplovinBannerAdManager`. Place banner ads in `fragment_emoji_battery.xml` and `fragment_customize.xml`.

*   **Task 5.2: Implement Premium Unlock Flow**
    *   **Update:** Use the existing `ApplovinRewardedAdManager` for the "Watch Ad" unlock mechanism. The logic in `AnimationActivity.kt` is a good reference.

*   **Task 5.3: Implement Ad Removal** 
    *   **Action:** Implement the "ADS ON/OFF" toggle using an in-app purchase. Store the ad-free status in the `AppRepository` or the new `CustomizationRepository`.